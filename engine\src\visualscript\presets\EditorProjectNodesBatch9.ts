/**
 * 编辑器项目管理节点
 * 第9批次：编辑器项目管理（节点251-270）
 * 包含项目创建、打开、保存、资产管理、场景编辑等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node, NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';

// ============================================================================
// 编辑器项目管理节点（251-258）
// ============================================================================

/**
 * 创建项目节点 (251)
 * 创建新的编辑器项目
 */
export class CreateProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'projectName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '项目名称',
      defaultValue: '新项目'
    });
    this.addInput({
      name: 'template',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '项目模板',
      defaultValue: 'empty'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目对象'
    });
  }

  public execute(): any {
    try {
      const projectNameInput = this.getInput('projectName');
      const templateInput = this.getInput('template');

      const projectName = projectNameInput?.value || '新项目';
      const template = templateInput?.value || 'empty';

      // 创建项目对象
      const project = {
        id: Date.now().toString(),
        name: projectName,
        template: template,
        createdAt: new Date(),
        scenes: [],
        assets: [],
        settings: {
          version: '1.0.0',
          renderer: 'webgl',
          physics: 'cannon'
        }
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = project;

      return project;
    } catch (error) {
      console.error('创建项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = null;

      return null;
    }
  }
}

/**
 * 打开项目节点 (252)
 * 打开现有项目
 */
export class OpenProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'projectPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '项目路径'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目对象'
    });
  }

  public execute(): any {
    try {
      const projectPathInput = this.getInput('projectPath');
      const projectPath = projectPathInput?.value;

      if (!projectPath) {
        throw new Error('项目路径不能为空');
      }

      // 模拟加载项目
      const project = {
        id: 'loaded-project',
        name: '已加载项目',
        path: projectPath,
        loadedAt: new Date(),
        scenes: [],
        assets: []
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = project;

      return project;
    } catch (error) {
      console.error('打开项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = null;

      return null;
    }
  }
}

/**
 * 保存项目节点 (253)
 * 保存当前项目
 */
export class SaveProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'savePath',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '保存路径'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'success',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.OUTPUT,
      description: '保存成功'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const savePath = inputs.savePath;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 更新项目保存信息
      project.savedAt = new Date();
      if (savePath) {
        project.path = savePath;
      }

      // 模拟保存操作
      console.log('保存项目:', project.name, '到路径:', savePath);

      return {
        completed: true,
        success: true
      };
    } catch (error) {
      console.error('保存项目失败:', error);
      return {
        completed: false,
        success: false
      };
    }
  }
}

/**
 * 关闭项目节点 (254)
 * 关闭当前项目
 */
export class CloseProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'saveBeforeClose',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.INPUT,
      description: '关闭前保存',
      defaultValue: true
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'closed',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.OUTPUT,
      description: '已关闭'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const saveBeforeClose = inputs.saveBeforeClose !== false;

      if (project && saveBeforeClose) {
        // 关闭前保存
        project.savedAt = new Date();
        console.log('关闭前保存项目:', project.name);
      }

      // 清理项目资源
      if (project) {
        project.closed = true;
        project.closedAt = new Date();
      }

      return {
        completed: true,
        closed: true
      };
    } catch (error) {
      console.error('关闭项目失败:', error);
      return {
        completed: false,
        closed: false
      };
    }
  }
}

/**
 * 导出项目节点 (255)
 * 导出项目文件
 */
export class ExportProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'exportFormat',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '导出格式',
      defaultValue: 'zip'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'exportData',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '导出数据'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const exportFormat = inputs.exportFormat || 'zip';

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 创建导出数据
      const exportData = {
        format: exportFormat,
        project: project,
        exportedAt: new Date(),
        size: '1.2MB' // 模拟文件大小
      };

      return {
        completed: true,
        exportData: exportData
      };
    } catch (error) {
      console.error('导出项目失败:', error);
      return {
        completed: false,
        exportData: null
      };
    }
  }
}

/**
 * 导入项目节点 (256)
 * 导入项目文件
 */
export class ImportProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'projectData',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目数据'
    });
    this.addSocket({
      name: 'importPath',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '导入路径'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '项目对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const projectData = inputs.projectData;
      const importPath = inputs.importPath;

      if (!projectData) {
        throw new Error('项目数据不能为空');
      }

      // 创建导入的项目对象
      const project = {
        ...projectData,
        importedAt: new Date(),
        importPath: importPath,
        id: Date.now().toString()
      };

      return {
        completed: true,
        project: project
      };
    } catch (error) {
      console.error('导入项目失败:', error);
      return {
        completed: false,
        project: null
      };
    }
  }
}

/**
 * 设置项目配置节点 (257)
 * 配置项目参数
 */
export class SetProjectSettingsNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'settings',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '配置设置'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '项目对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const settings = inputs.settings;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 更新项目设置
      if (settings) {
        project.settings = {
          ...project.settings,
          ...settings,
          updatedAt: new Date()
        };
      }

      return {
        completed: true,
        project: project
      };
    } catch (error) {
      console.error('设置项目配置失败:', error);
      return {
        completed: false,
        project: inputs.project
      };
    }
  }
}

/**
 * 获取项目信息节点 (258)
 * 获取项目基本信息
 */
export class GetProjectInfoNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'projectInfo',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '项目信息'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 提取项目信息
      const projectInfo = {
        id: project.id,
        name: project.name,
        template: project.template,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        savedAt: project.savedAt,
        path: project.path,
        settings: project.settings,
        sceneCount: project.scenes ? project.scenes.length : 0,
        assetCount: project.assets ? project.assets.length : 0
      };

      return {
        completed: true,
        projectInfo: projectInfo
      };
    } catch (error) {
      console.error('获取项目信息失败:', error);
      return {
        completed: false,
        projectInfo: null
      };
    }
  }
}

// ============================================================================
// 资产管理节点（259-265）
// ============================================================================

/**
 * 导入资产节点 (259)
 * 导入外部资产文件
 */
export class ImportAssetNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'assetPath',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '资产路径'
    });
    this.addSocket({
      name: 'assetType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '资产类型',
      defaultValue: 'auto'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '资产对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const assetPath = inputs.assetPath;
      const assetType = inputs.assetType || 'auto';

      if (!project || !assetPath) {
        throw new Error('项目对象和资产路径不能为空');
      }

      // 创建资产对象
      const asset = {
        id: Date.now().toString(),
        name: assetPath.split('/').pop() || 'unknown',
        path: assetPath,
        type: assetType,
        importedAt: new Date(),
        size: '1MB' // 模拟文件大小
      };

      // 添加到项目资产列表
      if (!project.assets) {
        project.assets = [];
      }
      project.assets.push(asset);

      return {
        completed: true,
        asset: asset
      };
    } catch (error) {
      console.error('导入资产失败:', error);
      return {
        completed: false,
        asset: null
      };
    }
  }
}

/**
 * 删除资产节点 (260)
 * 删除项目资产
 */
export class DeleteAssetNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'assetId',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '资产ID'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'deleted',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.OUTPUT,
      description: '删除成功'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const assetId = inputs.assetId;

      if (!project || !assetId) {
        throw new Error('项目对象和资产ID不能为空');
      }

      // 从项目中删除资产
      if (project.assets) {
        const index = project.assets.findIndex((asset: any) => asset.id === assetId);
        if (index !== -1) {
          project.assets.splice(index, 1);
          return {
            completed: true,
            deleted: true
          };
        }
      }

      return {
        completed: true,
        deleted: false
      };
    } catch (error) {
      console.error('删除资产失败:', error);
      return {
        completed: false,
        deleted: false
      };
    }
  }
}

/**
 * 重命名资产节点 (261)
 * 重命名资产文件
 */
export class RenameAssetNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '资产对象'
    });
    this.addSocket({
      name: 'newName',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '新名称'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '资产对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const asset = inputs.asset;
      const newName = inputs.newName;

      if (!asset || !newName) {
        throw new Error('资产对象和新名称不能为空');
      }

      // 重命名资产
      asset.name = newName;
      asset.updatedAt = new Date();

      return {
        completed: true,
        asset: asset
      };
    } catch (error) {
      console.error('重命名资产失败:', error);
      return {
        completed: false,
        asset: inputs.asset
      };
    }
  }
}

/**
 * 移动资产节点 (262)
 * 移动资产到文件夹
 */
export class MoveAssetNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '资产对象'
    });
    this.addSocket({
      name: 'targetFolder',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '目标文件夹'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '资产对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const asset = inputs.asset;
      const targetFolder = inputs.targetFolder;

      if (!asset || !targetFolder) {
        throw new Error('资产对象和目标文件夹不能为空');
      }

      // 移动资产
      asset.folder = targetFolder;
      asset.updatedAt = new Date();

      return {
        completed: true,
        asset: asset
      };
    } catch (error) {
      console.error('移动资产失败:', error);
      return {
        completed: false,
        asset: inputs.asset
      };
    }
  }
}

/**
 * 创建文件夹节点 (263)
 * 创建资产文件夹
 */
export class CreateFolderNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'project',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '项目对象'
    });
    this.addSocket({
      name: 'folderName',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '文件夹名称'
    });
    this.addSocket({
      name: 'parentFolder',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '父文件夹',
      defaultValue: 'root'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'folder',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '文件夹对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const project = inputs.project;
      const folderName = inputs.folderName;
      const parentFolder = inputs.parentFolder || 'root';

      if (!project || !folderName) {
        throw new Error('项目对象和文件夹名称不能为空');
      }

      // 创建文件夹对象
      const folder = {
        id: Date.now().toString(),
        name: folderName,
        parent: parentFolder,
        type: 'folder',
        createdAt: new Date(),
        children: []
      };

      // 添加到项目文件夹列表
      if (!project.folders) {
        project.folders = [];
      }
      project.folders.push(folder);

      return {
        completed: true,
        folder: folder
      };
    } catch (error) {
      console.error('创建文件夹失败:', error);
      return {
        completed: false,
        folder: null
      };
    }
  }
}

/**
 * 获取资产信息节点 (264)
 * 获取资产详细信息
 */
export class GetAssetInfoNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '资产对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'assetInfo',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '资产信息'
    });
  }

  public execute(inputs: any): any {
    try {
      const asset = inputs.asset;

      if (!asset) {
        throw new Error('资产对象不能为空');
      }

      // 提取资产信息
      const assetInfo = {
        id: asset.id,
        name: asset.name,
        type: asset.type,
        path: asset.path,
        size: asset.size,
        createdAt: asset.createdAt,
        updatedAt: asset.updatedAt,
        folder: asset.folder
      };

      return {
        completed: true,
        assetInfo: assetInfo
      };
    } catch (error) {
      console.error('获取资产信息失败:', error);
      return {
        completed: false,
        assetInfo: null
      };
    }
  }
}

/**
 * 生成缩略图节点 (265)
 * 生成资产预览图
 */
export class GenerateThumbnailNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'asset',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '资产对象'
    });
    this.addSocket({
      name: 'size',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '缩略图大小',
      defaultValue: 128
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'thumbnail',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '缩略图'
    });
  }

  public execute(inputs: any): any {
    try {
      const asset = inputs.asset;
      const size = inputs.size || 128;

      if (!asset) {
        throw new Error('资产对象不能为空');
      }

      // 生成缩略图
      const thumbnail = {
        assetId: asset.id,
        size: size,
        url: `thumbnail_${asset.id}_${size}.png`,
        generatedAt: new Date()
      };

      // 添加缩略图到资产
      asset.thumbnail = thumbnail;

      return {
        completed: true,
        thumbnail: thumbnail
      };
    } catch (error) {
      console.error('生成缩略图失败:', error);
      return {
        completed: false,
        thumbnail: null
      };
    }
  }
}

// ============================================================================
// 场景编辑节点（266-270）
// ============================================================================

/**
 * 创建实体节点 (266)
 * 在场景中创建新实体
 */
export class CreateEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'entityType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '实体类型',
      defaultValue: 'empty'
    });
    this.addSocket({
      name: 'entityName',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '实体名称',
      defaultValue: '新实体'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const scene = inputs.scene;
      const entityType = inputs.entityType || 'empty';
      const entityName = inputs.entityName || '新实体';

      if (!scene) {
        throw new Error('场景对象不能为空');
      }

      // 创建实体对象
      const entity = new THREE.Object3D();
      entity.name = entityName;
      entity.userData = {
        type: entityType,
        createdAt: new Date()
      };

      // 添加到场景
      scene.add(entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('创建实体失败:', error);
      return {
        completed: false,
        entity: null
      };
    }
  }
}

/**
 * 删除实体节点 (267)
 * 从场景删除实体
 */
export class DeleteEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'deleted',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.OUTPUT,
      description: '删除成功'
    });
  }

  public execute(inputs: any): any {
    try {
      const scene = inputs.scene;
      const entity = inputs.entity;

      if (!scene || !entity) {
        throw new Error('场景对象和实体对象不能为空');
      }

      // 从场景中删除实体
      scene.remove(entity);

      return {
        completed: true,
        deleted: true
      };
    } catch (error) {
      console.error('删除实体失败:', error);
      return {
        completed: false,
        deleted: false
      };
    }
  }
}

/**
 * 选择实体节点 (268)
 * 选择场景中的实体
 */
export class SelectEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'selectedEntity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '选中的实体'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 标记实体为选中状态
      entity.userData = entity.userData || {};
      entity.userData.selected = true;
      entity.userData.selectedAt = new Date();

      return {
        completed: true,
        selectedEntity: entity
      };
    } catch (error) {
      console.error('选择实体失败:', error);
      return {
        completed: false,
        selectedEntity: null
      };
    }
  }
}

/**
 * 复制实体节点 (269)
 * 复制选中的实体
 */
export class DuplicateEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'duplicatedEntity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '复制的实体'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;
      const scene = inputs.scene;

      if (!entity || !scene) {
        throw new Error('实体对象和场景对象不能为空');
      }

      // 复制实体
      const duplicatedEntity = entity.clone();
      duplicatedEntity.name = entity.name + '_copy';
      duplicatedEntity.position.x += 1; // 稍微偏移位置

      // 添加到场景
      scene.add(duplicatedEntity);

      return {
        completed: true,
        duplicatedEntity: duplicatedEntity
      };
    } catch (error) {
      console.error('复制实体失败:', error);
      return {
        completed: false,
        duplicatedEntity: null
      };
    }
  }
}

/**
 * 组合实体节点 (270)
 * 将多个实体组合
 */
export class GroupEntitiesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entities',
      type: SocketType.ARRAY,
      direction: SocketDirection.INPUT,
      description: '实体数组'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'groupName',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '组名称',
      defaultValue: '实体组'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'group',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体组'
    });
  }

  public execute(inputs: any): any {
    try {
      const entities = inputs.entities;
      const scene = inputs.scene;
      const groupName = inputs.groupName || '实体组';

      if (!entities || !Array.isArray(entities) || !scene) {
        throw new Error('实体数组和场景对象不能为空');
      }

      // 创建组对象
      const group = new THREE.Group();
      group.name = groupName;

      // 将实体添加到组中
      entities.forEach((entity: any) => {
        if (entity && entity.parent) {
          entity.parent.remove(entity);
        }
        group.add(entity);
      });

      // 将组添加到场景
      scene.add(group);

      return {
        completed: true,
        group: group
      };
    } catch (error) {
      console.error('组合实体失败:', error);
      return {
        completed: false,
        group: null
      };
    }
  }
}
